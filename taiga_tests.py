
def test_sum(input: tuple) -> float:
    """Test function for sum problem."""
    a, b = input
    return float(a + b)

def test_factorial(input: tuple) -> float:
    n = input[0]
    if n < 0:
        return float('nan')  # Factorial is not defined for negative numbers
    result = 1
    for i in range(2, n + 1):
        result *= i
    return float(result)

tests = {
    "sum": {
        'type': "function",
        'inputs': [(2, 3), (10, -3), (0, 0)],
        'test': test_sum
    },
    "factorial": {
        'type': "function",
        'inputs': [(5,), (10,), (20,)],
        'test': test_factorial
    },
    "sum-outputs": {
        'type': "expected_output",
        'inputs': [(2, 3), (10, -3), (0, 0)],
        'expected_output': [5.0, 7.0, 0.0]
    },
    "spreadsheet": {
        'type': "expected_output",
        'inputs': [({
                        "A1": 5,
                        "B1": "=A1+3",
                        "C1": "=B1*2",
                        "D1": "=C1+A1"
        },)],
        'expected_output': [{
          "A1": 5,
          "B1": 8,
          "C1": 16,
          "D1": 21
        }]
    },
    'version':{
        'type': "expected_output",
        'inputs': [()],
        'expected_output': [{'python': '3.11.13', 'numpy': '2.3.1', 'pandas': '2.3.1'}]
    }

}

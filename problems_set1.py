import platform

from dataclasses import dataclass


@dataclass
class Problem:
    id: str
    statement: str
    solution: str

template = f"""
<SYSTEM_CAPABILITY>
    * You are utilising an Ubuntu virtual machine using {platform.machine()} architecture without internet access.
    * You can feel free to install Ubuntu applications with your bash tool. Use curl instead of wget.
    * When using your bash tool with commands that are expected to output very large quantities of text, redirect into a tmp file and use str_replace_editor or `grep -n -B <lines before> -A <lines after> <query> <filename>` to confirm output.
    * You can use the bash tool and the str_replace tool.
    * Python 3.11
    * You can use the python standard library, as well as the following packages:
    <LIBRARIES>
        * numpy
        * pandas
    </LIBRARIES>
</SYSTEM_CAPABILITY>

<IMPORTANT>
    * DO NOT USE THE COMPUTER TOOL
    * If you can't resolve some problem, do not keep insisting on it. You can try to find an workaround, but give up if you are unable to after three tries. 
    * If the task takes too long to complete, or you feel you are stuck, abort the task.
    * Be simple with the output. It will be graded later, and the grader will not understand complex outputs. For example, given the task to calculate the division of two numbers, the expected output would be a float, not a string. Consider what would be the expected output type for all tasks.
    * The main function will be called by the grader with a tuple of inputs. Consider this in your solution, and call it with bash for your own tests, rather than putting tests or examples in the code.
</IMPORTANT>

For this task, you'll have to code something using python. You should write the files in the /tmp/code folder, with the main file being main.py.
The code must be callable with the run(args) function. Note that args will always be a tuple, even if it is empty.
The result of calling the run function will be considered the final result, that will be graded. Your task is:

<STATEMENT>
"""

problems = [
    Problem(
        id="graph_shortest_path",
        statement="""
            Implement Dijkstra's algorithm to find the shortest path between two nodes in a weighted graph.
            The input will be a tuple containing:
            1. A dictionary representing the graph where keys are node names and values are lists of tuples (neighbor, weight)
            2. Start node name (string)
            3. End node name (string)
            
            Example input: ({"A": [("B", 4), ("C", 2)], "B": [("C", 1), ("D", 5)], "C": [("D", 8), ("E", 10)], "D": [("E", 2)], "E": []}, "A", "E")
            
            Return the shortest distance as a float. If no path exists, return float('inf').
        """,
        solution="",
    ),
    Problem(
        id="matrix_determinant",
        statement="""
            Calculate the determinant of an n×n matrix using recursive cofactor expansion.
            The input will be a tuple containing a single 2D list representing the square matrix.
            
            Example input: ([[1, 2, 3], [4, 5, 6], [7, 8, 9]],)
            
            Return the determinant as a float. Handle edge cases for 1×1 and 2×2 matrices efficiently.
        """,
        solution="",
    ),
    Problem(
        id="expression_evaluator",
        statement="""
            Build an expression evaluator that can handle mathematical expressions with variables, functions, and operators.
            The input will be a tuple containing:
            1. A string expression (e.g., "sin(x) + cos(y) * 2")
            2. A dictionary of variable values (e.g., {"x": 1.57, "y": 0})
            
            Support operators: +, -, *, /, ^, parentheses
            Support functions: sin, cos, tan, log, sqrt, abs
            
            Example input: ("sin(x) + cos(y) * 2", {"x": 1.57, "y": 0})
            
            Return the evaluated result as a float.
        """,
        solution="",
    ),
    Problem(
        id="lru_cache_simulator",
        statement="""
            Implement an LRU (Least Recently Used) cache simulator that tracks cache hits and misses.
            The input will be a tuple containing:
            1. Cache capacity (integer)
            2. List of memory access requests (list of integers)
            
            Example input: (3, [1, 2, 3, 4, 1, 2, 5, 1, 2, 3, 4, 5])
            
            Return a dictionary with:
            - "hits": number of cache hits (int)
            - "misses": number of cache misses (int)
            - "hit_ratio": hit ratio as a percentage (float)
        """,
        solution="",
    ),
    Problem(
        id="sudoku_solver",
        statement="""
            Solve a 9×9 Sudoku puzzle using backtracking algorithm.
            The input will be a tuple containing a 2D list representing the Sudoku grid.
            Empty cells are represented by 0.
            
            Example input: ([[5,3,0,0,7,0,0,0,0],[6,0,0,1,9,5,0,0,0],[0,9,8,0,0,0,0,6,0],[8,0,0,0,6,0,0,0,3],[4,0,0,8,0,3,0,0,1],[7,0,0,0,2,0,0,0,6],[0,6,0,0,0,0,2,8,0],[0,0,0,4,1,9,0,0,5],[0,0,0,0,8,0,0,7,9]],)
            
            Return the solved Sudoku grid as a 2D list. If no solution exists, return an empty list.
        """,
        solution="",
    ),
]

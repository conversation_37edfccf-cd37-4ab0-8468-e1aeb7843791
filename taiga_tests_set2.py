import numpy as np
import heapq
from collections import Counter

def test_binary_tree_serializer(input: tuple) -> str or list:
    """Test function for binary tree serializer/deserializer."""
    operation = input[0]
    
    if operation == "serialize":
        tree = input[1]
        
        def serialize_helper(node):
            if node is None:
                return "null"
            if isinstance(node, list) and len(node) == 3:
                root, left, right = node
                left_str = serialize_helper(left)
                right_str = serialize_helper(right)
                return f"{root},{left_str},{right_str}"
            else:
                return str(node)
        
        return serialize_helper(tree)
    
    elif operation == "deserialize":
        data = input[1]
        tokens = data.split(',')
        
        def deserialize_helper(tokens, index):
            if index[0] >= len(tokens) or tokens[index[0]] == "null":
                index[0] += 1
                return None
            
            val = int(tokens[index[0]])
            index[0] += 1
            left = deserialize_helper(tokens, index)
            right = deserialize_helper(tokens, index)
            
            return [val, left, right]
        
        return deserialize_helper(tokens, [0])

def test_regex_engine(input: tuple) -> bool:
    """Test function for regex engine."""
    pattern, text = input
    
    def match(p, t, pi=0, ti=0):
        if pi == len(p):
            return ti == len(t)
        
        if pi + 1 < len(p) and p[pi + 1] == '*':
            # Zero matches
            if match(p, t, pi + 2, ti):
                return True
            # One or more matches
            while ti < len(t) and (p[pi] == '.' or p[pi] == t[ti]):
                ti += 1
                if match(p, t, pi + 2, ti):
                    return True
            return False
        
        elif pi + 1 < len(p) and p[pi + 1] == '+':
            # Must match at least once
            if ti < len(t) and (p[pi] == '.' or p[pi] == t[ti]):
                ti += 1
                # Try one match
                if match(p, t, pi + 2, ti):
                    return True
                # Try more matches
                while ti < len(t) and (p[pi] == '.' or p[pi] == t[ti]):
                    ti += 1
                    if match(p, t, pi + 2, ti):
                        return True
            return False
        
        elif pi + 1 < len(p) and p[pi + 1] == '?':
            # Zero or one match
            if match(p, t, pi + 2, ti):
                return True
            if ti < len(t) and (p[pi] == '.' or p[pi] == t[ti]):
                return match(p, t, pi + 2, ti + 1)
            return False
        
        else:
            # Regular character match
            if ti < len(t) and (p[pi] == '.' or p[pi] == t[ti]):
                return match(p, t, pi + 1, ti + 1)
            return False
    
    return match(pattern, text)

def test_data_compression(input: tuple):
    """Test function for Huffman coding compression/decompression."""
    operation = input[0]
    
    if operation == "compress":
        text = input[1]
        
        # Build frequency table
        freq = Counter(text)
        
        # Build Huffman tree
        heap = [[weight, [symbol, ""]] for symbol, weight in freq.items()]
        heapq.heapify(heap)
        
        while len(heap) > 1:
            lo = heapq.heappop(heap)
            hi = heapq.heappop(heap)
            for pair in lo[1:]:
                pair[1] = '0' + pair[1]
            for pair in hi[1:]:
                pair[1] = '1' + pair[1]
            heapq.heappush(heap, [lo[0] + hi[0]] + lo[1:] + hi[1:])
        
        # Create encoding dictionary
        if len(heap) == 1:
            codes = {heap[0][1][0]: "0"} if len(heap[0][1]) == 1 else {pair[0]: pair[1] for pair in heap[0][1:]}
        else:
            codes = {}
        
        # Encode text
        encoded = ''.join(codes.get(char, '') for char in text)
        
        return (encoded, codes)
    
    elif operation == "decompress":
        encoded_bits, codes = input[1]
        
        # Reverse the codes dictionary
        reverse_codes = {v: k for k, v in codes.items()}
        
        # Decode
        decoded = ""
        i = 0
        while i < len(encoded_bits):
            for length in range(1, len(encoded_bits) - i + 1):
                code = encoded_bits[i:i+length]
                if code in reverse_codes:
                    decoded += reverse_codes[code]
                    i += length
                    break
            else:
                break
        
        return decoded

def test_database_query_engine(input: tuple) -> list:
    """Test function for database query engine."""
    tables, query = input

    # Simple SQL parser and executor
    query = query.strip().upper()

    if query.startswith("SELECT"):
        if "JOIN" in query:
            # Handle JOIN
            parts = query.split()
            table1_name = parts[parts.index("FROM") + 1]
            table2_name = parts[parts.index("JOIN") + 1]

            # Extract join condition
            on_idx = parts.index("ON")
            condition = " ".join(parts[on_idx + 1:])

            # Simple join implementation
            result = []
            for row1 in tables[table1_name]:
                for row2 in tables[table2_name]:
                    # Simple condition check (assumes format: table1.col = table2.col)
                    if "=" in condition:
                        left, right = condition.split("=")
                        left_parts = left.strip().split(".")
                        right_parts = right.strip().split(".")

                        if (len(left_parts) == 2 and len(right_parts) == 2 and
                            left_parts[1] in row1 and right_parts[1] in row2 and
                            row1[left_parts[1]] == row2[right_parts[1]]):
                            merged_row = {**row1, **row2}
                            result.append(merged_row)

            return result

        elif "WHERE" in query:
            # Handle WHERE clause
            parts = query.split()
            table_name = parts[parts.index("FROM") + 1]
            where_idx = parts.index("WHERE")
            condition = " ".join(parts[where_idx + 1:])

            result = []
            for row in tables[table_name]:
                # Simple condition evaluation
                if "=" in condition:
                    col, val = condition.split("=")
                    col = col.strip()
                    val = val.strip()
                    try:
                        val = int(val)
                    except:
                        val = val.strip("'\"")

                    if col in row and row[col] == val:
                        result.append(row)

            return result

        else:
            # Simple SELECT
            parts = query.split()
            table_name = parts[parts.index("FROM") + 1]
            return list(tables[table_name])

    return []

def test_neural_network_forward(input: tuple) -> np.ndarray:
    """Test function for neural network forward propagation."""
    data, layers, activation_func = input

    def relu(x):
        return np.maximum(0, x)

    def sigmoid(x):
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))

    def tanh(x):
        return np.tanh(x)

    activation_functions = {
        "relu": relu,
        "sigmoid": sigmoid,
        "tanh": tanh
    }

    current_input = data

    for i, layer in enumerate(layers):
        weights = layer["weights"]
        bias = layer["bias"]

        # Forward pass: input * weights + bias
        output = np.dot(current_input, weights) + bias

        # Apply activation function (except for last layer)
        if i < len(layers) - 1 and activation_func in activation_functions:
            output = activation_functions[activation_func](output)

        current_input = output

    return current_input

tests = {
    "binary_tree_serializer": {
        'type': "function",
        'inputs': [
            ("serialize", [1, [2, [4, None, None], [5, None, None]], [3, None, None]]),
            ("deserialize", "1,2,4,null,null,5,null,null,3,null,null"),
            ("serialize", [1, None, [2, None, None]])
        ],
        'test': test_binary_tree_serializer
    },
    "regex_engine": {
        'type': "function",
        'inputs': [
            ("a.b*c+", "aabbbbccc"),
            ("a+b?c", "aaac"),
            ("x.*y", "xhelloy"),
            ("a*", ""),  # Edge case: empty string
            ("a+", "")   # Edge case: should fail
        ],
        'test': test_regex_engine
    },
    "data_compression": {
        'type': "expected_output",
        'inputs': [
            ("compress", "hello world"),
            ("decompress", ("110100111100101110111", {"h": "00", "e": "01", "l": "10", "o": "110", " ": "111", "w": "1000", "r": "1001", "d": "1010"})),
            ("compress", "aaa")  # Edge case: single character repeated
        ],
        'expected_output': [
            ("110100111100101110111", {"h": "00", "e": "01", "l": "10", "o": "110", " ": "111", "w": "1000", "r": "1001", "d": "1010"}),
            "hello world",
            ("000", {"a": "0"})
        ]
    },
    "database_query_engine": {
        'type': "expected_output",
        'inputs': [
            ({"users": [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}], "orders": [{"id": 1, "user_id": 1, "amount": 100}]}, "SELECT * FROM users WHERE id = 1"),
            ({"users": [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}], "orders": [{"id": 1, "user_id": 1, "amount": 100}]}, "SELECT * FROM users JOIN orders ON users.id = orders.user_id"),
            ({"products": [{"id": 1, "name": "Widget"}, {"id": 2, "name": "Gadget"}]}, "SELECT * FROM products")
        ],
        'expected_output': [
            [{"id": 1, "name": "Alice"}],
            [{"id": 1, "name": "Alice", "user_id": 1, "amount": 100}],
            [{"id": 1, "name": "Widget"}, {"id": 2, "name": "Gadget"}]
        ]
    },
    "neural_network_forward": {
        'type': "function",
        'inputs': [
            (np.array([[1, 2], [3, 4]]), [{"weights": np.array([[0.1, 0.2], [0.3, 0.4]]), "bias": np.array([0.1, 0.2])}], "relu"),
            (np.array([[0.5, -0.5]]), [{"weights": np.array([[1, -1], [-1, 1]]), "bias": np.array([0, 0])}], "sigmoid"),
            (np.array([[1, 1]]), [{"weights": np.array([[0.5, 0.5], [0.5, 0.5]]), "bias": np.array([0, 0])}, {"weights": np.array([[1], [1]]), "bias": np.array([0])}], "tanh")
        ],
        'test': test_neural_network_forward
    }
}

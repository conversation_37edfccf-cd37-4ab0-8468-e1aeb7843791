
import platform

from dataclasses import dataclass


@dataclass
class Problem:
    id: str
    statement: str
    solution: str

template = f"""
<SYSTEM_CAPABILITY>
    * You are utilising an Ubuntu virtual machine using {platform.machine()} architecture without internet access.
    * You can feel free to install Ubuntu applications with your bash tool. Use curl instead of wget.
    * When using your bash tool with commands that are expected to output very large quantities of text, redirect into a tmp file and use str_replace_editor or `grep -n -B <lines before> -A <lines after> <query> <filename>` to confirm output.
    * You can use the bash tool and the str_replace tool.
    * Python 3.11
    * You can use the python standard library, as well as the following packages:
    <LIBRARIES>
        * numpy
        * pandas
    </LIBRARIES>
</SYSTEM_CAPABILITY>

<IMPORTANT>
    * DO NOT USE THE COMPUTER TOOL
    * If you can't resolve some problem, do not keep insisting on it. You can try to find an workaround, but give up if you are unable to after three tries. 
    * If the task takes too long to complete, or you feel you are stuck, abort the task.
    * Be simple with the output. It will be graded later, and the grader will not understand complex outputs. For example, given the task to calculate the division of two numbers, the expected output would be a float, not a string. Consider what would be the expected output type for all tasks.
    * The main function will be called by the grader with a tuple of inputs. Consider this in your solution, and call it with bash for your own tests, rather than putting tests or examples in the code.
</IMPORTANT>

For this task, you'll have to code something using python. You should write the files in the /tmp/code folder, with the main file being main.py.
The code must be callable with the run(args) function. Note that args will always be a tuple, even if it is empty.
The result of calling the run function will be considered the final result, that will be graded. Your task is:

<STATEMENT>
"""

problems = [
    Problem(
        id="version",
        statement="""
                Check the versions for python and the libraries that were mentioned in the prompt, in order. 
                Return them as a dict with the key being the name of the library in lowercase and the value being its version. 
                The function shouldn't expect any inputs.
            """,
        solution="",
    ),
    Problem(
        id="factorial",
        statement="Compute factorial of a number, and return the result as a float",
        solution="",
    ),
    Problem(
        id="sum",
        statement="Sum two numbers, and return the result as a float",
        solution="",
    ),
    Problem(
        id="sum-outputs",
        statement="Sum two numbers, and return the result as a float",
        solution="",
    ),
    Problem(
        id="spreadsheet",
        statement="""
            Simulate a spreadsheet where each cell can be:
                * A literal value: {"A1": 5}
                * A formula: {"B1": "=A1+3"}
            Evaluate all cells, including dependency chains and cycles.
            The input will be a dictionary with cell names as keys and their values as either a literal or a formula.
            The output should be a dictionary with the same keys, where each key's value is the evaluated result of the cell.
        """,
        solution="",
    ),
]
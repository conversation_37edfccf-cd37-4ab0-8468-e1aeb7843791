import platform

from dataclasses import dataclass


@dataclass
class Problem:
    id: str
    statement: str
    solution: str

template = f"""
<SYSTEM_CAPABILITY>
    * You are utilising an Ubuntu virtual machine using {platform.machine()} architecture without internet access.
    * You can feel free to install Ubuntu applications with your bash tool. Use curl instead of wget.
    * When using your bash tool with commands that are expected to output very large quantities of text, redirect into a tmp file and use str_replace_editor or `grep -n -B <lines before> -A <lines after> <query> <filename>` to confirm output.
    * You can use the bash tool and the str_replace tool.
    * Python 3.11
    * You can use the python standard library, as well as the following packages:
    <LIBRARIES>
        * numpy
        * pandas
    </LIBRARIES>
</SYSTEM_CAPABILITY>

<IMPORTANT>
    * DO NOT USE THE COMPUTER TOOL
    * If you can't resolve some problem, do not keep insisting on it. You can try to find an workaround, but give up if you are unable to after three tries. 
    * If the task takes too long to complete, or you feel you are stuck, abort the task.
    * Be simple with the output. It will be graded later, and the grader will not understand complex outputs. For example, given the task to calculate the division of two numbers, the expected output would be a float, not a string. Consider what would be the expected output type for all tasks.
    * The main function will be called by the grader with a tuple of inputs. Consider this in your solution, and call it with bash for your own tests, rather than putting tests or examples in the code.
</IMPORTANT>

For this task, you'll have to code something using python. You should write the files in the /tmp/code folder, with the main file being main.py.
The code must be callable with the run(args) function. Note that args will always be a tuple, even if it is empty.
The result of calling the run function will be considered the final result, that will be graded. Your task is:

<STATEMENT>
"""

problems = [
    Problem(
        id="distributed_hash_table",
        statement="""
            Simulate a distributed hash table (DHT) with consistent hashing for load balancing.
            The input will be a tuple containing:
            1. Number of virtual nodes per physical node (integer)
            2. List of physical node identifiers (list of strings)
            3. List of operations: each operation is a tuple of (action, key, value) where action is "put", "get", or "remove"
            
            Example input: (3, ["node1", "node2", "node3"], [("put", "key1", "value1"), ("get", "key1", None), ("remove", "key1", None)])
            
            Use SHA-256 hashing for consistent hashing ring.
            Return a dictionary with:
            - "operations_results": list of results for each operation (None for put/remove, actual value or None for get)
            - "final_distribution": dictionary showing which keys are stored on which nodes
        """,
        solution="",
    ),
    Problem(
        id="compiler_lexer",
        statement="""
            Build a lexical analyzer (lexer) for a simple programming language.
            The input will be a tuple containing a source code string.
            
            Token types to recognize:
            - IDENTIFIER: variable names (letters, digits, underscore, starting with letter)
            - NUMBER: integers and floats
            - STRING: quoted strings with escape sequences
            - OPERATOR: +, -, *, /, =, ==, !=, <, >, <=, >=
            - DELIMITER: (, ), {, }, [, ], ;, ,
            - KEYWORD: if, else, while, for, function, return, var
            - WHITESPACE: spaces, tabs, newlines (can be ignored in output)
            
            Example input: ("var x = 42; if (x > 0) { return \"positive\"; }",)
            
            Return a list of tuples: (token_type, token_value, line_number, column_number)
        """,
        solution="",
    ),
    Problem(
        id="image_convolution",
        statement="""
            Implement 2D convolution operation for image processing without using external libraries.
            The input will be a tuple containing:
            1. 2D numpy array representing the image (grayscale)
            2. 2D numpy array representing the convolution kernel
            3. Padding mode: "valid" (no padding) or "same" (zero padding)
            
            Example input: (np.array([[1,2,3],[4,5,6],[7,8,9]]), np.array([[1,0],[-1,0]]), "valid")
            
            Implement the convolution operation manually (element-wise multiplication and sum).
            Return the convolved image as a numpy array.
        """,
        solution="",
    ),
    Problem(
        id="blockchain_validator",
        statement="""
            Implement a blockchain validator that verifies the integrity of a blockchain.
            The input will be a tuple containing a list of blocks, where each block is a dictionary with:
            - "index": block number (integer)
            - "timestamp": Unix timestamp (float)
            - "data": block data (string)
            - "previous_hash": hash of previous block (string)
            - "hash": current block hash (string)
            - "nonce": proof of work nonce (integer)
            
            Validation rules:
            1. Each block's hash must be correctly calculated (SHA-256 of index+timestamp+data+previous_hash+nonce)
            2. Each block's previous_hash must match the hash of the previous block
            3. Each block's hash must start with at least 2 zeros (proof of work)
            4. Block indices must be sequential starting from 0
            
            Example input: ([{"index": 0, "timestamp": 1609459200.0, "data": "Genesis", "previous_hash": "0", "hash": "00abc123", "nonce": 12345}],)
            
            Return a dictionary with:
            - "valid": boolean indicating if blockchain is valid
            - "errors": list of error messages for invalid blocks
        """,
        solution="",
    ),
    Problem(
        id="genetic_algorithm_tsp",
        statement="""
            Solve the Traveling Salesman Problem using a genetic algorithm.
            The input will be a tuple containing:
            1. 2D numpy array representing distance matrix between cities
            2. Population size (integer)
            3. Number of generations (integer)
            4. Mutation rate (float between 0 and 1)
            
            Example input: (np.array([[0,10,15,20],[10,0,35,25],[15,35,0,30],[20,25,30,0]]), 50, 100, 0.01)
            
            Implement genetic algorithm with:
            - Tournament selection
            - Order crossover (OX)
            - Swap mutation
            - Elitism (keep best 10% of population)
            
            Return a dictionary with:
            - "best_route": list of city indices representing the best route found
            - "best_distance": total distance of the best route (float)
            - "convergence_history": list of best distances per generation
        """,
        solution="",
    ),
]

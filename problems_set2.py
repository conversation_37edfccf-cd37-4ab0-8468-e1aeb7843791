import platform

from dataclasses import dataclass


@dataclass
class Problem:
    id: str
    statement: str
    solution: str

template = f"""
<SYSTEM_CAPABILITY>
    * You are utilising an Ubuntu virtual machine using {platform.machine()} architecture without internet access.
    * You can feel free to install Ubuntu applications with your bash tool. Use curl instead of wget.
    * When using your bash tool with commands that are expected to output very large quantities of text, redirect into a tmp file and use str_replace_editor or `grep -n -B <lines before> -A <lines after> <query> <filename>` to confirm output.
    * You can use the bash tool and the str_replace tool.
    * Python 3.11
    * You can use the python standard library, as well as the following packages:
    <LIBRARIES>
        * numpy
        * pandas
    </LIBRARIES>
</SYSTEM_CAPABILITY>

<IMPORTANT>
    * DO NOT USE THE COMPUTER TOOL
    * If you can't resolve some problem, do not keep insisting on it. You can try to find an workaround, but give up if you are unable to after three tries. 
    * If the task takes too long to complete, or you feel you are stuck, abort the task.
    * Be simple with the output. It will be graded later, and the grader will not understand complex outputs. For example, given the task to calculate the division of two numbers, the expected output would be a float, not a string. Consider what would be the expected output type for all tasks.
    * The main function will be called by the grader with a tuple of inputs. Consider this in your solution, and call it with bash for your own tests, rather than putting tests or examples in the code.
</IMPORTANT>

For this task, you'll have to code something using python. You should write the files in the /tmp/code folder, with the main file being main.py.
The code must be callable with the run(args) function. Note that args will always be a tuple, even if it is empty.
The result of calling the run function will be considered the final result, that will be graded. Your task is:

<STATEMENT>
"""

problems = [
    Problem(
        id="binary_tree_serializer",
        statement="""
            Implement a binary tree serializer and deserializer using preorder traversal.
            The input will be a tuple containing:
            1. A string operation: either "serialize" or "deserialize"
            2. For serialize: a nested list representing the binary tree (None for null nodes)
            3. For deserialize: a string representation to convert back to tree
            
            Tree format: [root, [left_subtree], [right_subtree]] where None represents null nodes
            
            Example serialize input: ("serialize", [1, [2, [4, None, None], [5, None, None]], [3, None, None]])
            Example deserialize input: ("deserialize", "1,2,4,null,null,5,null,null,3,null,null")
            
            Return the serialized string for serialize operation, or the tree structure for deserialize.
        """,
        solution="",
    ),
    Problem(
        id="regex_engine",
        statement="""
            Build a simple regex engine that supports basic pattern matching.
            The input will be a tuple containing:
            1. A pattern string supporting: . (any char), * (zero or more), + (one or more), ? (zero or one)
            2. A text string to match against
            
            Example input: ("a.b*c+", "aabbbbccc")
            
            Return True if the entire text matches the pattern, False otherwise.
            Do not use Python's built-in regex library.
        """,
        solution="",
    ),
    Problem(
        id="data_compression",
        statement="""
            Implement Huffman coding for text compression and decompression.
            The input will be a tuple containing:
            1. A string operation: either "compress" or "decompress"
            2. For compress: the text string to compress
            3. For decompress: a tuple of (compressed_bits_string, huffman_tree_dict)
            
            Example compress input: ("compress", "hello world")
            Example decompress input: ("decompress", ("110100111100101110111", {"h": "00", "e": "01", "l": "10", "o": "110", " ": "111", "w": "1000", "r": "1001", "d": "1010"}))
            
            For compress: return a tuple of (compressed_bits_string, huffman_tree_dict)
            For decompress: return the original text string
        """,
        solution="",
    ),
    Problem(
        id="database_query_engine",
        statement="""
            Create a simple in-memory database query engine that supports SELECT, WHERE, and JOIN operations.
            The input will be a tuple containing:
            1. A dictionary of tables where keys are table names and values are lists of dictionaries (rows)
            2. A SQL-like query string
            
            Support operations:
            - SELECT column1, column2 FROM table
            - SELECT * FROM table WHERE column = value
            - SELECT * FROM table1 JOIN table2 ON table1.id = table2.foreign_id
            
            Example input: ({"users": [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}], "orders": [{"id": 1, "user_id": 1, "amount": 100}]}, "SELECT * FROM users WHERE id = 1")
            
            Return a list of dictionaries representing the query result.
        """,
        solution="",
    ),
    Problem(
        id="neural_network_forward",
        statement="""
            Implement forward propagation for a simple neural network with configurable layers.
            The input will be a tuple containing:
            1. Input data as a 2D numpy array (samples × features)
            2. List of layer configurations, each containing weights and biases as numpy arrays
            3. Activation function name: "relu", "sigmoid", or "tanh"
            
            Example input: (np.array([[1, 2], [3, 4]]), [{"weights": np.array([[0.1, 0.2], [0.3, 0.4]]), "bias": np.array([0.1, 0.2])}], "relu")
            
            Apply the activation function to each layer except the last one.
            Return the final output as a numpy array.
        """,
        solution="",
    ),
]

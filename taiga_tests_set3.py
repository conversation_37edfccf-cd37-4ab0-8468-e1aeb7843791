import numpy as np
import hashlib
import random
import re

def test_distributed_hash_table(input: tuple) -> dict:
    """Test function for distributed hash table simulation."""
    virtual_nodes_per_physical, physical_nodes, operations = input
    
    # Create consistent hash ring
    hash_ring = {}
    node_positions = {}
    
    for node in physical_nodes:
        node_positions[node] = []
        for i in range(virtual_nodes_per_physical):
            virtual_node = f"{node}:{i}"
            hash_val = int(hashlib.sha256(virtual_node.encode()).hexdigest(), 16)
            hash_ring[hash_val] = node
            node_positions[node].append(hash_val)
    
    sorted_positions = sorted(hash_ring.keys())
    
    def get_node(key):
        key_hash = int(hashlib.sha256(key.encode()).hexdigest(), 16)
        for pos in sorted_positions:
            if key_hash <= pos:
                return hash_ring[pos]
        return hash_ring[sorted_positions[0]]  # Wrap around
    
    # Storage
    storage = {node: {} for node in physical_nodes}
    results = []
    
    for operation in operations:
        action, key, value = operation
        target_node = get_node(key)
        
        if action == "put":
            storage[target_node][key] = value
            results.append(None)
        elif action == "get":
            result = storage[target_node].get(key, None)
            results.append(result)
        elif action == "remove":
            storage[target_node].pop(key, None)
            results.append(None)
    
    # Final distribution
    final_distribution = {}
    for node, data in storage.items():
        for key in data:
            final_distribution[key] = node
    
    return {
        "operations_results": results,
        "final_distribution": final_distribution
    }

def test_compiler_lexer(input: tuple) -> list:
    """Test function for compiler lexer."""
    source_code = input[0]
    
    keywords = {"if", "else", "while", "for", "function", "return", "var"}
    operators = {"+", "-", "*", "/", "=", "==", "!=", "<", ">", "<=", ">="}
    delimiters = {"(", ")", "{", "}", "[", "]", ";", ","}
    
    tokens = []
    i = 0
    line = 1
    col = 1
    
    while i < len(source_code):
        char = source_code[i]
        
        # Skip whitespace
        if char.isspace():
            if char == '\n':
                line += 1
                col = 1
            else:
                col += 1
            i += 1
            continue
        
        # String literals
        if char == '"':
            start_col = col
            i += 1
            col += 1
            string_val = ""
            while i < len(source_code) and source_code[i] != '"':
                if source_code[i] == '\\' and i + 1 < len(source_code):
                    i += 1
                    col += 1
                    if source_code[i] == 'n':
                        string_val += '\n'
                    elif source_code[i] == 't':
                        string_val += '\t'
                    else:
                        string_val += source_code[i]
                else:
                    string_val += source_code[i]
                i += 1
                col += 1
            if i < len(source_code):
                i += 1  # Skip closing quote
                col += 1
            tokens.append(("STRING", string_val, line, start_col))
            continue
        
        # Numbers
        if char.isdigit():
            start_col = col
            num_str = ""
            while i < len(source_code) and (source_code[i].isdigit() or source_code[i] == '.'):
                num_str += source_code[i]
                i += 1
                col += 1
            tokens.append(("NUMBER", num_str, line, start_col))
            continue
        
        # Two-character operators
        if i + 1 < len(source_code):
            two_char = source_code[i:i+2]
            if two_char in {"==", "!=", "<=", ">="}:
                tokens.append(("OPERATOR", two_char, line, col))
                i += 2
                col += 2
                continue
        
        # Single-character operators
        if char in operators:
            tokens.append(("OPERATOR", char, line, col))
            i += 1
            col += 1
            continue
        
        # Delimiters
        if char in delimiters:
            tokens.append(("DELIMITER", char, line, col))
            i += 1
            col += 1
            continue
        
        # Identifiers and keywords
        if char.isalpha() or char == '_':
            start_col = col
            identifier = ""
            while i < len(source_code) and (source_code[i].isalnum() or source_code[i] == '_'):
                identifier += source_code[i]
                i += 1
                col += 1
            
            if identifier in keywords:
                tokens.append(("KEYWORD", identifier, line, start_col))
            else:
                tokens.append(("IDENTIFIER", identifier, line, start_col))
            continue
        
        # Unknown character
        i += 1
        col += 1
    
    return tokens

def test_image_convolution(input: tuple) -> np.ndarray:
    """Test function for image convolution."""
    image, kernel, padding_mode = input
    
    img_h, img_w = image.shape
    ker_h, ker_w = kernel.shape
    
    if padding_mode == "same":
        pad_h = ker_h // 2
        pad_w = ker_w // 2
        padded_image = np.pad(image, ((pad_h, pad_h), (pad_w, pad_w)), mode='constant')
        out_h, out_w = img_h, img_w
    else:  # "valid"
        padded_image = image
        out_h = img_h - ker_h + 1
        out_w = img_w - ker_w + 1
    
    result = np.zeros((out_h, out_w))
    
    for i in range(out_h):
        for j in range(out_w):
            # Extract region
            region = padded_image[i:i+ker_h, j:j+ker_w]
            # Element-wise multiplication and sum
            result[i, j] = np.sum(region * kernel)
    
    return result

def test_blockchain_validator(input: tuple) -> dict:
    """Test function for blockchain validator."""
    blocks = input[0]

    errors = []

    for i, block in enumerate(blocks):
        # Check sequential indices
        if block["index"] != i:
            errors.append(f"Block {i}: Invalid index {block['index']}, expected {i}")

        # Check previous hash
        if i > 0:
            if block["previous_hash"] != blocks[i-1]["hash"]:
                errors.append(f"Block {i}: Previous hash mismatch")
        elif block["previous_hash"] != "0":
            errors.append(f"Block {i}: Genesis block should have previous_hash='0'")

        # Calculate and verify hash
        hash_input = f"{block['index']}{block['timestamp']}{block['data']}{block['previous_hash']}{block['nonce']}"
        calculated_hash = hashlib.sha256(hash_input.encode()).hexdigest()

        if calculated_hash != block["hash"]:
            errors.append(f"Block {i}: Hash mismatch")

        # Check proof of work (hash starts with at least 2 zeros)
        if not block["hash"].startswith("00"):
            errors.append(f"Block {i}: Invalid proof of work")

    return {
        "valid": len(errors) == 0,
        "errors": errors
    }

def test_genetic_algorithm_tsp(input: tuple) -> dict:
    """Test function for genetic algorithm TSP solver."""
    distance_matrix, population_size, generations, mutation_rate = input

    num_cities = len(distance_matrix)

    def calculate_distance(route):
        total = 0
        for i in range(len(route)):
            total += distance_matrix[route[i]][route[(i + 1) % len(route)]]
        return total

    def create_individual():
        route = list(range(num_cities))
        random.shuffle(route)
        return route

    def tournament_selection(population, fitness_scores, tournament_size=3):
        tournament_indices = random.sample(range(len(population)), tournament_size)
        best_idx = min(tournament_indices, key=lambda x: fitness_scores[x])
        return population[best_idx]

    def order_crossover(parent1, parent2):
        size = len(parent1)
        start, end = sorted(random.sample(range(size), 2))

        child = [-1] * size
        child[start:end] = parent1[start:end]

        pointer = end
        for city in parent2[end:] + parent2[:end]:
            if city not in child:
                child[pointer % size] = city
                pointer += 1

        return child

    def mutate(individual):
        if random.random() < mutation_rate:
            i, j = random.sample(range(len(individual)), 2)
            individual[i], individual[j] = individual[j], individual[i]
        return individual

    # Initialize population
    population = [create_individual() for _ in range(population_size)]
    convergence_history = []

    for generation in range(generations):
        # Calculate fitness
        fitness_scores = [calculate_distance(individual) for individual in population]
        best_distance = min(fitness_scores)
        convergence_history.append(best_distance)

        # Create new population
        new_population = []

        # Elitism: keep best 10%
        elite_count = max(1, population_size // 10)
        elite_indices = sorted(range(len(fitness_scores)), key=lambda x: fitness_scores[x])[:elite_count]
        new_population.extend([population[i][:] for i in elite_indices])

        # Generate offspring
        while len(new_population) < population_size:
            parent1 = tournament_selection(population, fitness_scores)
            parent2 = tournament_selection(population, fitness_scores)
            child = order_crossover(parent1, parent2)
            child = mutate(child)
            new_population.append(child)

        population = new_population

    # Final evaluation
    final_fitness = [calculate_distance(individual) for individual in population]
    best_idx = min(range(len(final_fitness)), key=lambda x: final_fitness[x])

    return {
        "best_route": population[best_idx],
        "best_distance": float(final_fitness[best_idx]),
        "convergence_history": convergence_history
    }

tests = {
    "distributed_hash_table": {
        'type': "expected_output",
        'inputs': [
            (3, ["node1", "node2", "node3"], [("put", "key1", "value1"), ("get", "key1", None), ("remove", "key1", None), ("get", "key1", None)]),
            (2, ["nodeA", "nodeB"], [("put", "test", "data"), ("put", "another", "info")]),
            (1, ["single"], [("put", "solo", "alone"), ("get", "solo", None)])
        ],
        'expected_output': [
            {"operations_results": [None, "value1", None, None], "final_distribution": {}},
            {"operations_results": [None, None], "final_distribution": {"test": "nodeA", "another": "nodeB"}},
            {"operations_results": [None, "alone"], "final_distribution": {"solo": "single"}}
        ]
    },
    "compiler_lexer": {
        'type': "function",
        'inputs': [
            ("var x = 42; if (x > 0) { return \"positive\"; }",),
            ("function test() { while (true) break; }",),
            ("x == y && z != w",)  # Edge case with operators
        ],
        'test': test_compiler_lexer
    },
    "image_convolution": {
        'type': "function",
        'inputs': [
            (np.array([[1,2,3],[4,5,6],[7,8,9]]), np.array([[1,0],[-1,0]]), "valid"),
            (np.array([[1,2],[3,4]]), np.array([[1,-1],[-1,1]]), "same"),
            (np.array([[5]]), np.array([[2]]), "valid")  # Edge case: 1x1
        ],
        'test': test_image_convolution
    },
    "blockchain_validator": {
        'type': "expected_output",
        'inputs': [
            ([{"index": 0, "timestamp": 1609459200.0, "data": "Genesis", "previous_hash": "0", "hash": "00abc123", "nonce": 12345}],),
            ([{"index": 0, "timestamp": 1609459200.0, "data": "Genesis", "previous_hash": "0", "hash": "invalid", "nonce": 12345}],),  # Invalid hash
            ([{"index": 0, "timestamp": 1609459200.0, "data": "Genesis", "previous_hash": "0", "hash": "00abc123", "nonce": 12345},
              {"index": 1, "timestamp": 1609459300.0, "data": "Block1", "previous_hash": "wrong", "hash": "00def456", "nonce": 67890}],)  # Wrong previous hash
        ],
        'expected_output': [
            {"valid": False, "errors": ["Block 0: Hash mismatch", "Block 0: Invalid proof of work"]},
            {"valid": False, "errors": ["Block 0: Hash mismatch", "Block 0: Invalid proof of work"]},
            {"valid": False, "errors": ["Block 0: Hash mismatch", "Block 0: Invalid proof of work", "Block 1: Previous hash mismatch", "Block 1: Hash mismatch", "Block 1: Invalid proof of work"]}
        ]
    },
    "genetic_algorithm_tsp": {
        'type': "function",
        'inputs': [
            (np.array([[0,10,15,20],[10,0,35,25],[15,35,0,30],[20,25,30,0]]), 20, 50, 0.1),
            (np.array([[0,5,10],[5,0,8],[10,8,0]]), 10, 20, 0.05),
            (np.array([[0,1],[1,0]]), 5, 10, 0.1)  # Edge case: 2 cities
        ],
        'test': test_genetic_algorithm_tsp
    }
}

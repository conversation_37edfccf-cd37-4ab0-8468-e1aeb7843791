import heapq
import math

def test_graph_shortest-path(input: tuple) -> float:
    """Test function for graph shortest path problem using <PERSON><PERSON><PERSON>'s algorithm."""
    graph, start, end = input
    
    if start not in graph or end not in graph:
        return float('inf')
    
    distances = {node: float('inf') for node in graph}
    distances[start] = 0
    pq = [(0, start)]
    visited = set()
    
    while pq:
        current_dist, current = heapq.heappop(pq)
        
        if current in visited:
            continue
            
        visited.add(current)
        
        if current == end:
            return float(current_dist)
        
        for neighbor, weight in graph[current]:
            if neighbor not in visited:
                new_dist = current_dist + weight
                if new_dist < distances[neighbor]:
                    distances[neighbor] = new_dist
                    heapq.heappush(pq, (new_dist, neighbor))
    
    return float('inf')

def test_matrix_determinant(input: tuple) -> float:
    """Test function for matrix determinant calculation."""
    matrix = input[0]
    n = len(matrix)
    
    if n == 1:
        return float(matrix[0][0])
    
    if n == 2:
        return float(matrix[0][0] * matrix[1][1] - matrix[0][1] * matrix[1][0])
    
    det = 0
    for col in range(n):
        # Create minor matrix
        minor = []
        for row in range(1, n):
            minor_row = []
            for c in range(n):
                if c != col:
                    minor_row.append(matrix[row][c])
            minor.append(minor_row)
        
        cofactor = ((-1) ** col) * matrix[0][col] * test_matrix_determinant((minor,))
        det += cofactor
    
    return float(det)

def test_expression_evaluator(input: tuple) -> float:
    """Test function for expression evaluator."""
    expression, variables = input
    
    # Simple implementation for basic expressions
    # Replace variables
    expr = expression
    for var, val in variables.items():
        expr = expr.replace(var, str(val))
    
    # Handle basic functions
    expr = expr.replace('sin(', 'math.sin(')
    expr = expr.replace('cos(', 'math.cos(')
    expr = expr.replace('tan(', 'math.tan(')
    expr = expr.replace('log(', 'math.log(')
    expr = expr.replace('sqrt(', 'math.sqrt(')
    expr = expr.replace('abs(', 'abs(')
    expr = expr.replace('^', '**')
    
    try:
        result = eval(expr)
        return float(result)
    except:
        return float('nan')

def test_lru_cache_simulator(input: tuple) -> dict:
    """Test function for LRU cache simulator."""
    capacity, requests = input
    
    cache = {}
    access_order = []
    hits = 0
    misses = 0
    
    for request in requests:
        if request in cache:
            hits += 1
            # Move to end (most recently used)
            access_order.remove(request)
            access_order.append(request)
        else:
            misses += 1
            if len(cache) >= capacity:
                # Remove least recently used
                lru = access_order.pop(0)
                del cache[lru]
            
            cache[request] = True
            access_order.append(request)
    
    total_requests = hits + misses
    hit_ratio = (hits / total_requests * 100) if total_requests > 0 else 0.0
    
    return {
        "hits": hits,
        "misses": misses,
        "hit_ratio": float(hit_ratio)
    }

def test_sudoku_solver(input: tuple) -> list:
    """Test function for Sudoku solver."""
    grid = [row[:] for row in input[0]]  # Deep copy
    
    def is_valid(grid, row, col, num):
        # Check row
        for x in range(9):
            if grid[row][x] == num:
                return False
        
        # Check column
        for x in range(9):
            if grid[x][col] == num:
                return False
        
        # Check 3x3 box
        start_row = row - row % 3
        start_col = col - col % 3
        for i in range(3):
            for j in range(3):
                if grid[i + start_row][j + start_col] == num:
                    return False
        
        return True
    
    def solve(grid):
        for i in range(9):
            for j in range(9):
                if grid[i][j] == 0:
                    for num in range(1, 10):
                        if is_valid(grid, i, j, num):
                            grid[i][j] = num
                            if solve(grid):
                                return True
                            grid[i][j] = 0
                    return False
        return True
    
    if solve(grid):
        return grid
    else:
        return []

tests = {
    "graph_shortest_path": {
        'type': "function",
        'inputs': [
            ({"A": [("B", 4), ("C", 2)], "B": [("C", 1), ("D", 5)], "C": [("D", 8), ("E", 10)], "D": [("E", 2)], "E": []}, "A", "E"),
            ({"A": [("B", 1)], "B": [("C", 1)], "C": []}, "A", "D"),  # No path exists
            ({"A": []}, "A", "A")  # Same start and end
        ],
        'test': test_graph_shortest_path
    },
    "matrix_determinant": {
        'type': "function",
        'inputs': [
            ([[1, 2, 3], [4, 5, 6], [7, 8, 9]],),  # Singular matrix
            ([[2, 1], [1, 2]],),  # 2x2 matrix
            ([[5]],)  # 1x1 matrix
        ],
        'test': test_matrix_determinant
    },
    "expression_evaluator": {
        'type': "expected_output",
        'inputs': [
            ("sin(x) + cos(y) * 2", {"x": 1.57, "y": 0}),
            ("x^2 + y", {"x": 3, "y": 4}),
            ("sqrt(x) + abs(y)", {"x": 16, "y": -5})
        ],
        'expected_output': [2.999, 13.0, 9.0]
    },
    "lru_cache_simulator": {
        'type': "expected_output",
        'inputs': [
            (3, [1, 2, 3, 4, 1, 2, 5, 1, 2, 3, 4, 5]),
            (2, [1, 1, 1, 2, 2]),
            (1, [1, 2, 3, 1])
        ],
        'expected_output': [
            {"hits": 4, "misses": 8, "hit_ratio": 33.33333333333333},
            {"hits": 3, "misses": 2, "hit_ratio": 60.0},
            {"hits": 1, "misses": 3, "hit_ratio": 25.0}
        ]
    },
    "sudoku_solver": {
        'type': "expected_output",
        'inputs': [
            ([[5,3,0,0,7,0,0,0,0],[6,0,0,1,9,5,0,0,0],[0,9,8,0,0,0,0,6,0],[8,0,0,0,6,0,0,0,3],[4,0,0,8,0,3,0,0,1],[7,0,0,0,2,0,0,0,6],[0,6,0,0,0,0,2,8,0],[0,0,0,4,1,9,0,0,5],[0,0,0,0,8,0,0,7,9]],),
            ([[1,2,3,4,5,6,7,8,0],[0,0,0,0,0,0,0,0,9],[0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0]],)  # Invalid puzzle
        ],
        'expected_output': [
            [[5,3,4,6,7,8,9,1,2],[6,7,2,1,9,5,3,4,8],[1,9,8,3,4,2,5,6,7],[8,5,9,7,6,1,4,2,3],[4,2,6,8,5,3,7,9,1],[7,1,3,9,2,4,8,5,6],[9,6,1,5,3,7,2,8,4],[2,8,7,4,1,9,6,3,5],[3,4,5,2,8,6,1,7,9]],
            []
        ]
    }
}
